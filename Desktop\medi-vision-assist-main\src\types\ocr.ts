import { Word } from 'tesseract.js';

/**
 * Result interface for OCR operations
 */
export interface OCRResult {
  /** Extracted text from the image */
  text: string;
  /** Confidence score (0-100) */
  confidence: number;
  /** Processing time in milliseconds */
  processingTime: number;
  /** Array of word-level details */
  words: Word[];
  /** Whether the OCR operation was successful */
  success: boolean;
  /** Error message if operation failed */
  error?: string;
}

/**
 * Configuration options for OCR processing
 */
export interface OCRConfig {
  /** Language code for OCR (default: 'eng') */
  language?: string;
  /** Minimum confidence threshold (0-100) */
  minConfidence?: number;
  /** Enable debug logging */
  debug?: boolean;
  /** Custom Tesseract parameters */
  tesseractOptions?: Record<string, any>;
}

/**
 * Medicine-specific information extracted from OCR text
 */
export interface MedicineInfo {
  /** Potential medicine names found in text */
  potentialNames: string[];
  /** Dosage information if found */
  dosageInfo?: string;
  /** Medicine type/category */
  medicineType?: string;
  /** Overall confidence in medicine identification */
  confidence: number;
  /** Original OCR text */
  rawText: string;
  /** Cleaned and processed text */
  cleanedText: string;
}

/**
 * Result of medicine identification process
 */
export interface MedicineIdentificationResult {
  /** OCR extraction result */
  ocrResult: OCRResult;
  /** Medicine-specific information */
  medicineInfo: MedicineInfo;
  /** Final identified medicine name */
  identifiedMedicine?: string;
  /** Overall process success */
  success: boolean;
}

/**
 * Supabase storage structure for OCR results
 */
export interface OCRScanRecord {
  id?: string;
  user_id: string;
  image_url?: string; // Use existing column instead of image_name
  extracted_text: string;
  confidence_score: number;
  medicine_name?: string;
  medicine_type?: string;
  usage_info?: string;
  warnings?: string;
  side_effects?: string;
  scan_status: 'processing' | 'completed' | 'failed';
  created_at?: string;
}
